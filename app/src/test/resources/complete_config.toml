# this is a valid TOML config file

data-path="/opt/besu" # Path

#invalid-option=true

# network
discovery-enabled=false
poa-discovery-retry-bootnodes=true
bootnodes=[
  "enode://6f8a80d14311c39f35f516fa664deaaaa13e85b2f7493f37f6144d86991ec012937307647bd3b9a82abe2974e1407241d54947bbb39763a4cac9f77166ad92a0@***********:4567",
  "enode://6f8a80d14311c39f35f516fa664deaaaa13e85b2f7493f37f6144d86991ec012937307647bd3b9a82abe2974e1407241d54947bbb39763a4cac9f77166ad92a0@***********:4567",
  "enode://6f8a80d14311c39f35f516fa664deaaaa13e85b2f7493f37f6144d86991ec012937307647bd3b9a82abe2974e1407241d54947bbb39763a4cac9f77166ad92a0@***********:4567"
]
p2p-host="*******"
p2p-port=1234
max-peers=42
rpc-http-host="*******"
rpc-http-port=5678
graphql-http-host="*******"
graphql-http-port=6789
rpc-http-api=["ETH","WEB3"]
rpc-ws-host="**********"
rpc-ws-port=9101
rpc-ws-api=["ETH","WEB3"]
metrics-host="*******"
metrics-port=309

# chain
genesis-file="/opt/besu/genesis.json" # Path
network-id=42
sync-mode="fast"# should be FAST or FULL (or fast or full)
fast-sync-min-peers=13

#mining
miner-coinbase="******************************************"