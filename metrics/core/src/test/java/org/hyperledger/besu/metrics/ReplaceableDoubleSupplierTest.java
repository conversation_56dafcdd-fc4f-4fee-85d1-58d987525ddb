/*
 * Copyright contributors to Hyperledger Besu.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
package org.hyperledger.besu.metrics;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

public class ReplaceableDoubleSupplierTest {

  @Test
  public void shouldWorkAsNormalSupplier() {
    final var rds = new ReplaceableDoubleSupplier(() -> 1.0);
    assertThat(rds.getAsDouble()).isEqualTo(1.0);
  }

  @Test
  public void shouldReturnValueFromNewSupplierIfReplaced() {
    final var rds = new ReplaceableDoubleSupplier(() -> 1.0);
    assertThat(rds.getAsDouble()).isEqualTo(1.0);
    rds.replaceDoubleSupplier(() -> 2.0);
    assertThat(rds.getAsDouble()).isEqualTo(2.0);
  }
}
