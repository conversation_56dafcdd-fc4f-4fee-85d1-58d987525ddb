/*
 * Copyright contributors to Besu.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
package org.hyperledger.besu.ethereum.p2p.discovery.internal.packet.validation;

import org.hyperledger.besu.ethereum.p2p.discovery.DiscoveryPeer;

import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

public class DiscoveryPeersValidatorTest {
  private DiscoveryPeersValidator discoveryPeersValidator;

  @BeforeEach
  public void beforeTest() {
    discoveryPeersValidator = new DiscoveryPeersValidator();
  }

  @Test
  public void testValidateWithValidPeers() {
    Assertions.assertDoesNotThrow(
        () -> discoveryPeersValidator.validate(List.of(Mockito.mock(DiscoveryPeer.class))));
  }

  @Test
  public void testValidateWithInvalidPeers() {
    Assertions.assertThrows(
        IllegalArgumentException.class, () -> discoveryPeersValidator.validate(null));
  }
}
