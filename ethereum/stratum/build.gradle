/*
 * Copyright ConsenSys AG.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */

apply plugin: 'java-library'

jar {
  archiveBaseName = calculateArtifactId(project)
  manifest {
    attributes(
      'Specification-Title': archiveBaseName,
      'Specification-Version': project.version,
      'Implementation-Title': archiveBaseName,
      'Implementation-Version': calculateVersion(),
      'Commit-Hash': getGitCommitDetails(40).hash
      )
  }
}

dependencies {
  api project(':plugin-api')
  api project(':util')

  implementation project(':datatypes')
  implementation project(':ethereum:api')
  implementation project(':ethereum:core')
  implementation project(':ethereum:blockcreation')
  implementation project(':metrics:core')
  implementation project(':consensus:merge')

  implementation 'com.google.guava:guava'
  implementation 'io.vertx:vertx-core'
  implementation 'com.fasterxml.jackson.core:jackson-databind'
  implementation 'io.consensys.tuweni:tuweni-bytes'
  implementation 'io.consensys.tuweni:tuweni-units'

  testImplementation project(path: ':metrics:core', configuration: 'testSupportArtifacts')
  testImplementation project(':testutil')

  testImplementation 'com.fasterxml.jackson.core:jackson-databind'
  testImplementation 'io.vertx:vertx-junit5'
  testImplementation 'io.vertx:vertx-web-client'
  testImplementation 'org.assertj:assertj-core'
  testImplementation 'org.junit.jupiter:junit-jupiter'
  testImplementation 'org.mockito:mockito-core'
  testImplementation 'org.mockito:mockito-junit-jupiter'
}
