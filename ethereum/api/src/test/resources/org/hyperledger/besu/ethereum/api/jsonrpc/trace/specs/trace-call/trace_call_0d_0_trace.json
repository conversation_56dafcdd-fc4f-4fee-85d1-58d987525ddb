{"comment": "Sequential memory modifications with MSTORE.", "request": {"jsonrpc": "2.0", "method": "trace_call", "params": [{"from": "******************************************", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "value": "0x0"}, ["trace"], "latest"], "id": 13}, "response": {"jsonrpc": "2.0", "result": {"output": "0x", "stateDiff": null, "trace": [{"action": {"callType": "call", "from": "******************************************", "gas": "0xffadea", "input": "0x", "to": "******************************************", "value": "0x0"}, "result": {"gasUsed": "0xf", "output": "0x"}, "subtraces": 0, "traceAddress": [], "type": "call"}], "vmTrace": null}, "id": 13}, "statusCode": 200}