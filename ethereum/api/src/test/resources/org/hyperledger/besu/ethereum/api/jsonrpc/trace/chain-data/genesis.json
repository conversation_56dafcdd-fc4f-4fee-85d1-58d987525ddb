{"config": {"homesteadBlock": 0, "petersburgBlock": 0, "istanbulBlock": 0, "ethash": {"fixeddifficulty": 15}, "chainID": 1982, "networkID": 1982}, "nonce": "0x0000000000000042", "gasLimit": "0x100000000000", "difficulty": "0xf", "mixHash": "******************************************000000000000000000000000", "coinbase": "******************************************", "alloc": {"0010000000000000000000000000000000000000": {"comment": "Add smart contract that will simply set 2 key-value pairs.  Parses tx input into 4 32 byte values: [key1, val1, key2, val2]. Can be used to test constantinople fix.", "code": "0x6020356000355560603560403555", "balance": "0x0"}, "0020000000000000000000000000000000000000": {"comment": "Reads a single address from tx input, self-destructs and sends refund to input address.", "code": "0x600035FF", "balance": "0x300"}, "0030000000000000000000000000000000000000": {"comment": "Reads a 32 byte value from input data, increments it, and returns.", "code": "0x60003560010160005260206000F3", "balance": "0x0"}, "0040000000000000000000000000000000000000": {"comment": "0x outSize 6020 outOffset 6000 inputSize 60203603 inputToMem(dupSize 80)6020600037 inOffset 6000 val 34 to 600035 gas 5A call F1 Return 60206000F3", "code": "0x60206000602036038060206000376000346000355AF160206000F3", "balance": "0x0"}, "0050000000000000000000000000000000000000": {"comment": "0x outSize 6020 outOffset 6000 inputSize 60203603 inputToMem(dupSize 80)6020600037 inOffset 6000 val 34 to 600035 gas 5A CALLCODE F2 Return 60206000F3", "code": "0x60206000602036038060206000376000346000355AF260206000F3", "balance": "0x0"}, "0060000000000000000000000000000000000000": {"comment": "0x outSize 6020 outOffset 6000 inputSize 60203603 inputToMem(dupSize 80)6020600037 inOffset 6000 to 600035 gas 5A DELEGATECALL F4 Return 60206000F3", "code": "0x602060006020360380602060003760006000355AF460206000F3", "balance": "0x0"}, "0070000000000000000000000000000000000000": {"comment": "MSTORE8 0x1=0x2, MSTORE8 0x3=0x4, MSTORE8 0x1=0x5.", "code": "0x600160025360036004536001600553", "balance": "0x0"}, "0080000000000000000000000000000000000000": {"comment": "MSTORE 32 bytes.", "code": "0x7F00000000000000000000000000000000000000000000000000000000000000017F000000000000000000000000000000000000000000000000000000000000000152", "balance": "0x0"}, "0090000000000000000000000000000000000000": {"comment": "Increases storage 0x00 by one.  Return the new value", "code": "0x6000546001018060005360005560016000F3", "balance": "0x0"}, "00A0000000000000000000000000000000000000": {"comment": "Append log record with 1 topic using LOG1 OPCODE.", "code": "0x608B60045360FF60016004A1", "balance": "0x0"}, "00B0000000000000000000000000000000000000": {"comment": "Append log record with 2 topics using LOG2 OPCODE.", "code": "0x608B60045360AA60FF60016004A2", "balance": "0x0"}, "00C0000000000000000000000000000000000000": {"comment": "INVALID_OPERATION (0x1F).", "code": "0x60011F", "balance": "0x0"}, "00D0000000000000000000000000000000000000": {"comment": "INSUFFICIENT_STACK_ITEMS", "code": "0x600120", "balance": "0x0"}, "00E0000000000000000000000000000000000000": {"comment": "INVALID_JUMP_DESTINATION", "code": "0x60FF5660016002", "balance": "0x0"}, "00F0000000000000000000000000000000000000": {"comment": "pushes 1, 2, 3, then SWAP2 and SWAP1", "code": "0x6001600260039190", "balance": "0x0"}, "0100000000000000000000000000000000000000": {"comment": "Sequential memory modifications with MSTORE then read with MLOAD.", "code": "0x7F3940be4289e4c3587d88c1856cc95352461992db0a584c281226faefe560b3016000527F14c4d2c102bdeb2354bfc3dc96a95e4512cf3a8461e0560e2272dbf884ef3905601052600851", "balance": "0x0"}, "0110000000000000000000000000000000000000": {"comment": "Sequential memory modifications with MSTORE then REVERT", "code": "0x7F3940be4289e4c3587d88c1856cc95352461992db0a584c281226faefe560b3016000527F14c4d2c102bdeb2354bfc3dc96a95e4512cf3a8461e0560e2272dbf884ef390560105260086008fd", "balance": "0x0"}, "0120000000000000000000000000000000000000": {"comment": "0x outSize 6020 outOffset 6000 inputSize 60203603 inputToMem(dupSize 80)6020600037 inOffset 6000 val 34 to 600035 gas 5A STATICCALL F4 Return 60206000F3", "code": "0x60206000602036038060206000376000346000355AFA60206000F3", "balance": "0x0"}, "0130000000000000000000000000000000000000": {"comment": "{ (MSTORE 0 0x600160015560015460025561ffFF6000526002601eF3 ) (SSTORE 0 (CREATE2 0 11 31 0)) }", "code": "0x75600160015560015460025561ffFF6000526002601eF36000526016600a6000f060005500", "balance": "0x0"}, "0140000000000000000000000000000000000000": {"comment": "{ (MSTORE 0 0x600160015560015460025560FF60005360016000F3 ) (SSTORE 0 (CREATE2 0 11 31 0)) }", "code": "0x74600160015560015460025560FF60005360016000F360005260006015600b6000f560005500", "balance": "0x0"}, "0150000000000000000000000000000000000000": {"comment": "Same contract as 002. Reads a single address from tx input, self-destructs and sends refund to input address.", "code": "0x600035FF", "balance": "0x300"}, "0160000000000000000000000000000000000000": {"comment": "Contract retrieved from this reference test refund_CallToSuicideTwice_d0g0v0_Istanbul", "balance": "0x0de0b6b3a7640000", "code": "0x60006000600060006000730170000000000000000000000000000000000000600035f160005560006000600060006000730170000000000000000000000000000000000000600035f100"}, "0170000000000000000000000000000000000000": {"comment": "Contract retrieved from this reference test refund_CallToSuicideTwice_d0g0v0_Istanbul", "balance": "0x0de0b6b3a7640000", "code": "0x73095e7baea6a6c7c4c2dfeb977efac326af552d87ff00"}, "0180000000000000000000000000000000000000": {"comment": "Simple contract that creates another contract", "balance": "0x06", "code": "0x6b600c600055602060406000f0600052600d60146000f000"}, "627306090abaB3A6e1400e9345bC60c78a8BEf57": {"secretKey": "c87509a1c067bbde78beb793e6fa76530b6382a4c0241e5e4a9ec0a0f44dc0d3", "comment": "private key and this comment are ignored.  In a real chain, the private key should NOT be stored", "balance": "0xf000000000000000000000000"}, "fe3b557e8fb62b89f4916b721be55ceb828dbd73": {"secretKey": "8f2a55949038a9610f50fb23b5883af3b4ecb3c3bb792cbcefbd1542c692be63", "comment": "private key and this comment are ignored.  In a real chain, the private key should NOT be stored", "balance": "0xfffffffffffffffffffffffffffffffffffffffff"}, "f17f52151EbEF6C7334FAD080c5704D77216b732": {"secretKey": "ae6ae8e5ccbfb04590405997ee2d52d2b330726137b875053c36d94e974d162f", "comment": "private key and this comment are ignored.  In a real chain, the private key should NOT be stored", "balance": "0xf0000000000000000000000"}, "0000000000000000000000000000000000000001": {"balance": "0x1"}}}