{"request": {"jsonrpc": "2.0", "method": "trace_replayBlockTransactions", "params": ["0x12", ["trace"]], "id": 415}, "response": {"jsonrpc": "2.0", "result": [{"output": "0x", "stateDiff": null, "trace": [{"action": {"callType": "call", "from": "******************************************", "gas": "0xffadea", "input": "0x", "to": "******************************************", "value": "0x0"}, "result": {"gasUsed": "0x1e", "output": "0x"}, "subtraces": 0, "traceAddress": [], "type": "call"}], "transactionHash": "0x2a5079cc535c429f668f13a7fb9a28bdba6831b5462bd04f781777b332a8fcbd", "vmTrace": null}, {"output": "0x", "revertReason": "0x7d88c1856cc95352", "stateDiff": null, "trace": [{"action": {"callType": "call", "from": "******************************************", "gas": "0xffadea", "input": "0x", "to": "******************************************", "value": "0x0"}, "error": "Reverted", "revertReason": "0x7d88c1856cc95352", "subtraces": 0, "traceAddress": [], "type": "call"}], "transactionHash": "0xc388baa0e55e6b73e850b22dc7e9853700f6b995fd55d95dd6ccd5a13d63c566", "vmTrace": null}], "id": 415}, "statusCode": 200}