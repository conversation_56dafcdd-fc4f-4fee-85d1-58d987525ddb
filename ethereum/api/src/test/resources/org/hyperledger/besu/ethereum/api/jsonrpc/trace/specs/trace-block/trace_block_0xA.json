{"request": {"jsonrpc": "2.0", "method": "trace_block", "params": ["0xA"], "id": 415}, "response": {"jsonrpc": "2.0", "result": [{"action": {"callType": "call", "from": "******************************************", "gas": "0xffacc6", "input": "0x0000000000000000000000000030000000000000000000000000000000000000f000000000000000000000000000000000000000000000000000000000000001", "to": "******************************************", "value": "0x0"}, "blockHash": "0xf020928933dc70cc8b6034b6881501cb6cbf57cacd8cfb01ed6b2329e68e7073", "blockNumber": 10, "result": {"gasUsed": "0x30a", "output": "0xf000000000000000000000000000000000000000000000000000000000000002"}, "subtraces": 1, "traceAddress": [], "transactionHash": "0x4ec95b7de430b61fc9a57ed35274fd766b7f5fac5213ab946963eb528deae6b5", "transactionPosition": 0, "type": "call"}, {"action": {"callType": "callcode", "from": "******************************************", "gas": "0xfbab36", "input": "0xf000000000000000000000000000000000000000000000000000000000000001", "to": "0x0030000000000000000000000000000000000000", "value": "0x0"}, "blockHash": "0xf020928933dc70cc8b6034b6881501cb6cbf57cacd8cfb01ed6b2329e68e7073", "blockNumber": 10, "result": {"gasUsed": "0x1b", "output": "0xf000000000000000000000000000000000000000000000000000000000000002"}, "subtraces": 0, "traceAddress": [0], "transactionHash": "0x4ec95b7de430b61fc9a57ed35274fd766b7f5fac5213ab946963eb528deae6b5", "transactionPosition": 0, "type": "call"}, {"action": {"author": "0x0000000000000000000000000000000000000000", "rewardType": "block", "value": "0x1bc16d674ec80000"}, "blockHash": "0xf020928933dc70cc8b6034b6881501cb6cbf57cacd8cfb01ed6b2329e68e7073", "blockNumber": 10, "result": null, "subtraces": 0, "traceAddress": [], "transactionHash": null, "transactionPosition": null, "type": "reward"}], "id": 415}, "statusCode": 200}