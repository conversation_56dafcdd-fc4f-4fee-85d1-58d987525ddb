{"comment": "'Self destruct called in constructor with balance being returned to sender (from field)'", "request": {"method": "trace_callMany", "params": [[[{"from": "******************************************", "value": "0x0", "gas": "0xfffff2", "gasPrice": "0xef", "data": "0x60806040523373ffffffffffffffffffffffffffffffffffffffff16fffe"}, ["stateDiff"]]], "latest"], "id": 1, "jsonrpc": "2.0"}, "response": {"jsonrpc": "2.0", "result": [{"output": "0x", "stateDiff": {"******************************************": {"balance": {"*": {"from": "0x393f0f18385c0b29e", "to": "0x393f0f183863e8934"}}, "code": "=", "nonce": "=", "storage": {}}, "******************************************": {"balance": {"*": {"from": "0xefffffffffffe28d07a0be63b", "to": "0xefffffffffffe28d0798e0fa5"}}, "code": "=", "nonce": {"*": {"from": "0x17", "to": "0x18"}}, "storage": {}}}, "trace": [], "vmTrace": null}], "id": 1}, "statusCode": 200}