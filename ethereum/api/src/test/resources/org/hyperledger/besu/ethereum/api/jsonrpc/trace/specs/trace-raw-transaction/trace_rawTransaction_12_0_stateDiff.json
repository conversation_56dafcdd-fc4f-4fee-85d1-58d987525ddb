{"comment": "Memory Read.", "request": {"jsonrpc": "2.0", "method": "trace_rawTransaction", "params": ["0xf8611e81ef83fffff294010000000000000000000000000000000000000080801ca0408dba3acbde3b0670b4d987973960c5b44708e0c15937ac0e4230b3bb7affc3a0031bd0cffae1a36edd36d3683f05b76afd85e52953eacbcd99c063e01ffa2f5b", ["stateDiff"]], "id": 23}, "response": {"jsonrpc": "2.0", "result": {"output": "0x", "stateDiff": {"******************************************": {"balance": {"*": {"from": "0x393f0f18385c0b29e", "to": "0x393f0f183860d6418"}}, "code": "=", "nonce": "=", "storage": {}}, "******************************************": {"balance": {"*": {"from": "0xffffffffffffffffffffffffffffffffd27fb671b", "to": "0xffffffffffffffffffffffffffffffffd27aeb5a1"}}, "code": "=", "nonce": {"*": {"from": "0x1e", "to": "0x1f"}}, "storage": {}}}, "trace": [], "vmTrace": null}, "id": 23}, "statusCode": 200}