{"request": [{"jsonrpc": "2.0", "method": "trace_transaction", "params": ["0xd32c538fd1b3ef854c04ae39925dc9e849b568b92ea388f778b794851ab37f7c"], "id": 415}, {"jsonrpc": "2.0", "method": "trace_transaction", "params": ["0xaf151fc10eb21e76efb0bc74da179a674083e420280fb2985f76b0326682864a"], "id": 415}], "response": [{"jsonrpc": "2.0", "result": [{"action": {"callType": "call", "from": "******************************************", "gas": "0xffadea", "input": "0x", "to": "******************************************", "value": "0x0"}, "blockHash": "0x82066eb29b4f58f6ee0f8190e45efb31b3708397f0c147bf7a9bd3d1cae94e9b", "blockNumber": 20, "result": {"gasUsed": "0x16c46", "output": "0x"}, "subtraces": 1, "traceAddress": [], "transactionHash": "0xd32c538fd1b3ef854c04ae39925dc9e849b568b92ea388f778b794851ab37f7c", "transactionPosition": 0, "type": "call"}, {"action": {"creationMethod": "create", "from": "******************************************", "gas": "0xfb3412", "init": "0x600160015560015460025561ffff6000526002601ef3", "value": "0x0"}, "blockHash": "0x82066eb29b4f58f6ee0f8190e45efb31b3708397f0c147bf7a9bd3d1cae94e9b", "blockNumber": 20, "result": {"address": "******************************************", "code": "0xffff", "gasUsed": "0xa10e"}, "subtraces": 0, "traceAddress": [0], "transactionHash": "0xd32c538fd1b3ef854c04ae39925dc9e849b568b92ea388f778b794851ab37f7c", "transactionPosition": 0, "type": "create"}], "id": 415}, {"jsonrpc": "2.0", "result": [{"action": {"callType": "call", "from": "******************************************", "gas": "0xffadea", "input": "0x", "to": "******************************************", "value": "0x0"}, "blockHash": "0x82066eb29b4f58f6ee0f8190e45efb31b3708397f0c147bf7a9bd3d1cae94e9b", "blockNumber": 20, "result": {"gasUsed": "0x16b87", "output": "0x"}, "subtraces": 1, "traceAddress": [], "transactionHash": "0xaf151fc10eb21e76efb0bc74da179a674083e420280fb2985f76b0326682864a", "transactionPosition": 1, "type": "call"}, {"action": {"creationMethod": "create2", "from": "******************************************", "gas": "0xfb3409", "init": "0x600160015560015460025560ff60005360016000f3", "value": "0x0"}, "blockHash": "0x82066eb29b4f58f6ee0f8190e45efb31b3708397f0c147bf7a9bd3d1cae94e9b", "blockNumber": 20, "result": {"address": "******************************************", "code": "0xff", "gasUsed": "0xa046"}, "subtraces": 0, "traceAddress": [0], "transactionHash": "0xaf151fc10eb21e76efb0bc74da179a674083e420280fb2985f76b0326682864a", "transactionPosition": 1, "type": "create"}], "id": 415}], "statusCode": 200}