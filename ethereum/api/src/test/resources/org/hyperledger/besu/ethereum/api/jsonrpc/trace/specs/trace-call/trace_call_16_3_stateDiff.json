{"comment": "Clear contract storage keys 1 and 2", "request": {"jsonrpc": "2.0", "method": "trace_call", "params": [{"from": "******************************************", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "value": "0x0", "data": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000"}, ["stateDiff"], "latest"], "id": 40}, "response": {"jsonrpc": "2.0", "result": {"output": "******************************************000000000000000000000001", "stateDiff": {"******************************************": {"balance": {"*": {"from": "0x393f0f18385c0b29e", "to": "0x393f0f1838618776b"}}, "code": "=", "nonce": "=", "storage": {}}, "******************************************": {"balance": {"*": {"from": "0xefffffffffffe28d07a0be63b", "to": "0xefffffffffffe28d079b4216e"}}, "code": "=", "nonce": {"*": {"from": "0x17", "to": "0x18"}}, "storage": {}}}, "trace": [], "vmTrace": null}, "id": 40}, "statusCode": 200}