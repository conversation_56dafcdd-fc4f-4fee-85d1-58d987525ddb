{"request": {"jsonrpc": "2.0", "method": "trace_replayBlockTransactions", "params": ["0x16", ["trace", "vmTrace", "stateDiff"]], "id": 415}, "response": {"jsonrpc": "2.0", "result": [{"output": "******************************************000000000000000000000001", "stateDiff": {"******************************************": {"balance": {"*": {"from": "0x246ddf97c548ad80f", "to": "0x246ddf97c556ebd44"}}, "code": "=", "nonce": "=", "storage": {}}, "******************************************": {"balance": "=", "code": "=", "nonce": "=", "storage": {"******************************************000000000000000000000001": {"*": {"from": "******************************************000000000000000000000000", "to": "******************************************000000000000000000000001"}}, "******************************************000000000000000000000002": {"*": {"from": "******************************************000000000000000000000000", "to": "******************************************000000000000000000000002"}}}}, "0x627306090abab3a6e1400e9345bc60c78a8bef57": {"balance": {"*": {"from": "0xefffffffffffffffff8a48b3c", "to": "0xefffffffffffffffff7c0a607"}}, "code": "=", "nonce": {"*": {"from": "0xe", "to": "0xf"}}, "storage": {}}}, "trace": [{"action": {"callType": "call", "from": "0x627306090abab3a6e1400e9345bc60c78a8bef57", "gas": "0xffab2e", "input": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002", "to": "******************************************", "value": "0x0"}, "result": {"gasUsed": "0x9f57", "output": "******************************************000000000000000000000001"}, "subtraces": 1, "traceAddress": [], "type": "call"}, {"action": {"callType": "delegatecall", "from": "******************************************", "gas": "0xfba995", "input": "******************************************000000000000000000000001000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002", "to": "0x0010000000000000000000000000000000000000", "value": "0x0"}, "result": {"gasUsed": "0x9c58", "output": "0x"}, "subtraces": 0, "traceAddress": [0], "type": "call"}], "transactionHash": "0x1f74ac428df5427f3a5576869e870cfff6712e4cffb1506bb4ef8f36c5e48162", "vmTrace": {"code": "0x602060006020360380602060003760006000355af460206000f3", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755499}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755496}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755493}, "pc": 4, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xa0"], "store": null, "used": 16755491}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x80"], "store": null, "used": 16755488}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x80", "0x80"], "store": null, "used": 16755485}, "pc": 8, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755482}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755479}, "pc": 11, "sub": null}, {"cost": 27, "ex": {"mem": {"data": "******************************************000000000000000000000001000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002", "off": 0}, "push": [], "store": null, "used": 16755452}, "pc": 13, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755449}, "pc": 14, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755446}, "pc": 16, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x10000000000000000000000000000000000000"], "store": null, "used": 16755443}, "pc": 18, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xffaaf1"], "store": null, "used": 16755441}, "pc": 19, "sub": null}, {"cost": 16493649, "ex": {"mem": {"data": "******************************************000000000000000000000001", "off": 0}, "push": ["0x1"], "store": null, "used": 16714717}, "pc": 20, "sub": {"code": "0x6020356000355560603560403555", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16492946}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16492943}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16492940}, "pc": 3, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16492937}, "pc": 5, "sub": null}, {"cost": 20000, "ex": {"mem": null, "push": [], "store": {"key": "0x1", "val": "0x1"}, "used": 16472937}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x60"], "store": null, "used": 16472934}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x2"], "store": null, "used": 16472931}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x40"], "store": null, "used": 16472928}, "pc": 10, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x2"], "store": null, "used": 16472925}, "pc": 12, "sub": null}, {"cost": 20000, "ex": {"mem": null, "push": [], "store": {"key": "0x2", "val": "0x2"}, "used": 16452925}, "pc": 13, "sub": null}]}}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16714714}, "pc": 21, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16714711}, "pc": 23, "sub": null}, {"cost": 0, "ex": {"mem": null, "push": [], "store": null, "used": 16714711}, "pc": 25, "sub": null}]}}, {"output": "******************************************000000000000000000000001", "stateDiff": {"******************************************": {"balance": {"*": {"from": "0x246ddf97c556ebd44", "to": "0x246ddf97c55e53aa9"}}, "code": "=", "nonce": "=", "storage": {}}, "******************************************": {"balance": "=", "code": "=", "nonce": "=", "storage": {"******************************************000000000000000000000001": {"*": {"from": "******************************************000000000000000000000001", "to": "******************************************000000000000000000000003"}}, "******************************************000000000000000000000002": {"*": {"from": "******************************************000000000000000000000002", "to": "******************************************000000000000000000000004"}}}}, "0x627306090abab3a6e1400e9345bc60c78a8bef57": {"balance": {"*": {"from": "0xefffffffffffffffff7c0a607", "to": "0xefffffffffffffffff74a28a2"}}, "code": "=", "nonce": {"*": {"from": "0xf", "to": "0x10"}}, "storage": {}}}, "trace": [{"action": {"callType": "call", "from": "0x627306090abab3a6e1400e9345bc60c78a8bef57", "gas": "0xffab2e", "input": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000004", "to": "******************************************", "value": "0x0"}, "result": {"gasUsed": "0x2a27", "output": "******************************************000000000000000000000001"}, "subtraces": 1, "traceAddress": [], "type": "call"}, {"action": {"callType": "delegatecall", "from": "******************************************", "gas": "0xfba995", "input": "******************************************000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000004", "to": "0x0010000000000000000000000000000000000000", "value": "0x0"}, "result": {"gasUsed": "0x2728", "output": "0x"}, "subtraces": 0, "traceAddress": [0], "type": "call"}], "transactionHash": "0x43d6ba1caeced03b6fa8cc3549c0557eea52917f1de50f4da23c9642beca95ee", "vmTrace": {"code": "0x602060006020360380602060003760006000355af460206000f3", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755499}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755496}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755493}, "pc": 4, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xa0"], "store": null, "used": 16755491}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x80"], "store": null, "used": 16755488}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x80", "0x80"], "store": null, "used": 16755485}, "pc": 8, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755482}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755479}, "pc": 11, "sub": null}, {"cost": 27, "ex": {"mem": {"data": "******************************************000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000004", "off": 0}, "push": [], "store": null, "used": 16755452}, "pc": 13, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755449}, "pc": 14, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755446}, "pc": 16, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x10000000000000000000000000000000000000"], "store": null, "used": 16755443}, "pc": 18, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xffaaf1"], "store": null, "used": 16755441}, "pc": 19, "sub": null}, {"cost": 16493649, "ex": {"mem": {"data": "******************************************000000000000000000000001", "off": 0}, "push": ["0x1"], "store": null, "used": 16744717}, "pc": 20, "sub": {"code": "0x6020356000355560603560403555", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16492946}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x3"], "store": null, "used": 16492943}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16492940}, "pc": 3, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16492937}, "pc": 5, "sub": null}, {"cost": 5000, "ex": {"mem": null, "push": [], "store": {"key": "0x1", "val": "0x3"}, "used": 16487937}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x60"], "store": null, "used": 16487934}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16487931}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x40"], "store": null, "used": 16487928}, "pc": 10, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x2"], "store": null, "used": 16487925}, "pc": 12, "sub": null}, {"cost": 5000, "ex": {"mem": null, "push": [], "store": {"key": "0x2", "val": "0x4"}, "used": 16482925}, "pc": 13, "sub": null}]}}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16744714}, "pc": 21, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16744711}, "pc": 23, "sub": null}, {"cost": 0, "ex": {"mem": null, "push": [], "store": null, "used": 16744711}, "pc": 25, "sub": null}]}}, {"output": "******************************************000000000000000000000001", "stateDiff": {"******************************************": {"balance": {"*": {"from": "0x246ddf97c55e53aa9", "to": "0x246ddf97c5618cbad"}}, "code": "=", "nonce": "=", "storage": {}}, "******************************************": {"balance": "=", "code": "=", "nonce": "=", "storage": {"******************************************000000000000000000000001": {"*": {"from": "******************************************000000000000000000000003", "to": "******************************************000000000000000000000000"}}}}, "0x627306090abab3a6e1400e9345bc60c78a8bef57": {"balance": {"*": {"from": "0xefffffffffffffffff74a28a2", "to": "0xefffffffffffffffff716979e"}}, "code": "=", "nonce": {"*": {"from": "0x10", "to": "0x11"}}, "storage": {}}}, "trace": [{"action": {"callType": "call", "from": "0x627306090abab3a6e1400e9345bc60c78a8bef57", "gas": "0xffab3a", "input": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000", "to": "******************************************", "value": "0x0"}, "result": {"gasUsed": "0x19bf", "output": "******************************************000000000000000000000001"}, "subtraces": 1, "traceAddress": [], "type": "call"}, {"action": {"callType": "delegatecall", "from": "******************************************", "gas": "0xfba9a0", "input": "******************************************000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000", "to": "0x0010000000000000000000000000000000000000", "value": "0x0"}, "result": {"gasUsed": "0x16c0", "output": "0x"}, "subtraces": 0, "traceAddress": [0], "type": "call"}], "transactionHash": "0x9eb50f31fc1d953e27331cd923f6b2f7fa11827d399c70aec00a04cf98cfd2ac", "vmTrace": {"code": "0x602060006020360380602060003760006000355af460206000f3", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755511}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755508}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755505}, "pc": 4, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xa0"], "store": null, "used": 16755503}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x80"], "store": null, "used": 16755500}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x80", "0x80"], "store": null, "used": 16755497}, "pc": 8, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755494}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755491}, "pc": 11, "sub": null}, {"cost": 27, "ex": {"mem": {"data": "******************************************000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000", "off": 0}, "push": [], "store": null, "used": 16755464}, "pc": 13, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755461}, "pc": 14, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755458}, "pc": 16, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x10000000000000000000000000000000000000"], "store": null, "used": 16755455}, "pc": 18, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xffaafd"], "store": null, "used": 16755453}, "pc": 19, "sub": null}, {"cost": 16493660, "ex": {"mem": {"data": "******************************************000000000000000000000001", "off": 0}, "push": ["0x1"], "store": null, "used": 16748929}, "pc": 20, "sub": {"code": "0x6020356000355560603560403555", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16492957}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x3"], "store": null, "used": 16492954}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16492951}, "pc": 3, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16492948}, "pc": 5, "sub": null}, {"cost": 800, "ex": {"mem": null, "push": [], "store": {"key": "0x1", "val": "0x3"}, "used": 16492148}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x60"], "store": null, "used": 16492145}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16492142}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x40"], "store": null, "used": 16492139}, "pc": 10, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16492136}, "pc": 12, "sub": null}, {"cost": 5000, "ex": {"mem": null, "push": [], "store": {"key": "0x1", "val": "0x0"}, "used": 16487136}, "pc": 13, "sub": null}]}}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16748926}, "pc": 21, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16748923}, "pc": 23, "sub": null}, {"cost": 0, "ex": {"mem": null, "push": [], "store": null, "used": 16748923}, "pc": 25, "sub": null}]}}, {"output": "******************************************000000000000000000000001", "stateDiff": {"******************************************": {"balance": {"*": {"from": "0x246ddf97c5618cbad", "to": "0x246ddf97c564c5717"}}, "code": "=", "nonce": "=", "storage": {}}, "******************************************": {"balance": "=", "code": "=", "nonce": "=", "storage": {"******************************************000000000000000000000002": {"*": {"from": "******************************************000000000000000000000004", "to": "******************************************000000000000000000000000"}}}}, "0x627306090abab3a6e1400e9345bc60c78a8bef57": {"balance": {"*": {"from": "0xefffffffffffffffff716979e", "to": "0xefffffffffffffffff6e30c34"}}, "code": "=", "nonce": {"*": {"from": "0x11", "to": "0x12"}}, "storage": {}}}, "trace": [{"action": {"callType": "call", "from": "0x627306090abab3a6e1400e9345bc60c78a8bef57", "gas": "0xffab46", "input": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000", "to": "******************************************", "value": "0x0"}, "result": {"gasUsed": "0x19bf", "output": "******************************************000000000000000000000001"}, "subtraces": 1, "traceAddress": [], "type": "call"}, {"action": {"callType": "delegatecall", "from": "******************************************", "gas": "0xfba9ac", "input": "******************************************000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000", "to": "0x0010000000000000000000000000000000000000", "value": "0x0"}, "result": {"gasUsed": "0x16c0", "output": "0x"}, "subtraces": 0, "traceAddress": [0], "type": "call"}], "transactionHash": "0xe2ef09a0b71f50947bd0bd1cacac77903d2a5fce80f34862bd4559727e0f608c", "vmTrace": {"code": "0x602060006020360380602060003760006000355af460206000f3", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755523}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755520}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755517}, "pc": 4, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xa0"], "store": null, "used": 16755515}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x80"], "store": null, "used": 16755512}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x80", "0x80"], "store": null, "used": 16755509}, "pc": 8, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755506}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755503}, "pc": 11, "sub": null}, {"cost": 27, "ex": {"mem": {"data": "******************************************000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000", "off": 0}, "push": [], "store": null, "used": 16755476}, "pc": 13, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755473}, "pc": 14, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755470}, "pc": 16, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x10000000000000000000000000000000000000"], "store": null, "used": 16755467}, "pc": 18, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xffab09"], "store": null, "used": 16755465}, "pc": 19, "sub": null}, {"cost": 16493672, "ex": {"mem": {"data": "******************************************000000000000000000000001", "off": 0}, "push": ["0x1"], "store": null, "used": 16748941}, "pc": 20, "sub": {"code": "0x6020356000355560603560403555", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16492969}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16492966}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16492963}, "pc": 3, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16492960}, "pc": 5, "sub": null}, {"cost": 800, "ex": {"mem": null, "push": [], "store": {"key": "0x1", "val": "0x0"}, "used": 16492160}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x60"], "store": null, "used": 16492157}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16492154}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x40"], "store": null, "used": 16492151}, "pc": 10, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x2"], "store": null, "used": 16492148}, "pc": 12, "sub": null}, {"cost": 5000, "ex": {"mem": null, "push": [], "store": {"key": "0x2", "val": "0x0"}, "used": 16487148}, "pc": 13, "sub": null}]}}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16748938}, "pc": 21, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16748935}, "pc": 23, "sub": null}, {"cost": 0, "ex": {"mem": null, "push": [], "store": null, "used": 16748935}, "pc": 25, "sub": null}]}}], "id": 415}, "statusCode": 200}