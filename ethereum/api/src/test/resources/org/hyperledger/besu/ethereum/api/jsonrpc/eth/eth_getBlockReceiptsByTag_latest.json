{"request": {"id": 307, "jsonrpc": "2.0", "method": "eth_getBlockReceipts", "params": ["latest"]}, "response": {"jsonrpc": "2.0", "id": 307, "result": [{"blockHash": "0x4859ea10d276f6df988eabd48a02e0696cfd3deb769d2a5e0e416a5f3191028a", "blockNumber": "0x22", "contractAddress": null, "cumulativeGasUsed": "0x5b22", "from": "******************************************", "gasUsed": "0x5b22", "effectiveGasPrice": "0x6fd1ca4a", "logs": [], "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "status": "0x1", "to": "******************************************", "transactionHash": "0x7c80f35a47f1d432d46add30f262c45f9578e2b1352dbaca0256af1ff3235532", "transactionIndex": "0x0", "type": "0x3", "blobGasUsed": "0x40000", "blobGasPrice": "0x1"}]}, "statusCode": 200}