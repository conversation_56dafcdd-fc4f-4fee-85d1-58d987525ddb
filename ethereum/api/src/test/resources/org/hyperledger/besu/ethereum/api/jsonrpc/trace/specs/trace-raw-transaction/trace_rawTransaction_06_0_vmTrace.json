{"comment": "Self destruct and send contract balance to address provided in the data field.", "request": {"jsonrpc": "2.0", "method": "trace_rawTransaction", "params": ["0xf8751e81ef83fffff2940020000000000000000000000000000000000000809400000000000000000000000000000000000009991ca00b4d27419dabf8945fd306fa1ec4ce0ee4765ff26f5d6eb15389dc92fd2c770da04958c967c1fa4192ce833a92e4d16c27db1fa06e00206fe146953ca6267878bf", ["vmTrace"]], "id": 112}, "response": {"jsonrpc": "2.0", "result": {"output": "0x", "stateDiff": null, "trace": [], "vmTrace": {"code": "0x", "ops": []}}, "id": 112}, "statusCode": 200}