{"comment": "Call to a contract creation that fails with a stack underflow", "request": {"jsonrpc": "2.0", "method": "trace_get", "params": ["0xf0378e1bf579b23aff89255d93ade14fe9136a7cedd9c4985e9bec2d18ce65e9", []], "id": 389}, "response": {"jsonrpc": "2.0", "result": {"action": {"creationMethod": "create", "from": "******************************************", "gas": "0x46e824", "init": "0x602af300", "value": "0x0"}, "blockHash": "0x156fa7f1633c5dd82d41b7c7d79049f620b25638ca87da137d13e1a27a210e49", "blockNumber": 33, "error": "Stack underflow", "subtraces": 0, "traceAddress": [], "transactionHash": "0xf0378e1bf579b23aff89255d93ade14fe9136a7cedd9c4985e9bec2d18ce65e9", "transactionPosition": 0, "type": "create"}, "id": 389}, "statusCode": 200}