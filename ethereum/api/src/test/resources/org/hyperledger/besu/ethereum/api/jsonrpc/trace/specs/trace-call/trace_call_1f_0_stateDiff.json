{"comment": "Call Precompiled contract directly", "request": {"jsonrpc": "2.0", "method": "trace_call", "params": [{"from": "******************************************", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "value": "0x0"}, ["stateDiff"], "latest"], "id": 50}, "response": {"jsonrpc": "2.0", "result": {"output": "0x", "stateDiff": {"******************************************": {"balance": {"*": {"from": "0x393f0f18385c0b29e", "to": "0x393f0f183861838de"}}, "code": "=", "nonce": "=", "storage": {}}, "******************************************": {"balance": {"*": {"from": "0xefffffffffffe28d07a0be63b", "to": "0xefffffffffffe28d079b45ffb"}}, "code": "=", "nonce": {"*": {"from": "0x17", "to": "0x18"}}, "storage": {}}}, "trace": [], "vmTrace": null}, "id": 50}, "statusCode": 200}