{"comment": "Proxy call to another contract - 1 level deep. Using CALLCODE.", "request": {"jsonrpc": "2.0", "method": "trace_call", "params": [{"from": "******************************************", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "value": "0x0", "data": "0x0000000000000000000000000030000000000000000000000000000000000000f000000000000000000000000000000000000000000000000000000000000001"}, ["trace"], "latest"], "id": 10}, "response": {"jsonrpc": "2.0", "result": {"output": "0xf000000000000000000000000000000000000000000000000000000000000002", "stateDiff": null, "trace": [{"action": {"callType": "call", "from": "******************************************", "gas": "0xffacc6", "input": "0x0000000000000000000000000030000000000000000000000000000000000000f000000000000000000000000000000000000000000000000000000000000001", "to": "******************************************", "value": "0x0"}, "result": {"gasUsed": "0x30a", "output": "0xf000000000000000000000000000000000000000000000000000000000000002"}, "subtraces": 1, "traceAddress": [], "type": "call"}, {"action": {"callType": "callcode", "from": "******************************************", "gas": "0xfbab36", "input": "0xf000000000000000000000000000000000000000000000000000000000000001", "to": "0x0030000000000000000000000000000000000000", "value": "0x0"}, "result": {"gasUsed": "0x1b", "output": "0xf000000000000000000000000000000000000000000000000000000000000002"}, "subtraces": 0, "traceAddress": [0], "type": "call"}], "vmTrace": null}, "id": 10}, "statusCode": 200}