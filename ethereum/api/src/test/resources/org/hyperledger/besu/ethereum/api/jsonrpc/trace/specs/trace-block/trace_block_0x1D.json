{"request": {"jsonrpc": "2.0", "method": "trace_block", "params": ["0x1D"], "id": 415}, "response": {"jsonrpc": "2.0", "result": [{"action": {"creationMethod": "create", "from": "******************************************", "gas": "0x4a14", "init": "0x6000f1", "value": "0x0"}, "blockHash": "0x3cecd9469acbef39e092a08b5a2ce8b2b6a12e54e88bcb953a4e0e458fb3cf6d", "blockNumber": 29, "error": "Stack underflow", "subtraces": 0, "traceAddress": [], "transactionHash": "0xf2f9f56aab631d441ff7bd85a195cea43c7285a4e0bd03823f6c37b379eb0cbe", "transactionPosition": 0, "type": "create"}, {"action": {"author": "******************************************", "rewardType": "block", "value": "0x1bc16d674ec80000"}, "blockHash": "0x3cecd9469acbef39e092a08b5a2ce8b2b6a12e54e88bcb953a4e0e458fb3cf6d", "blockNumber": 29, "result": null, "subtraces": 0, "traceAddress": [], "transactionHash": null, "transactionPosition": null, "type": "reward"}], "id": 415}, "statusCode": 200}