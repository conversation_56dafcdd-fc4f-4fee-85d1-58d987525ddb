{"comment": "Pushes 1, 2, 3, then SWAP2 and SWAP1.", "request": {"jsonrpc": "2.0", "method": "trace_call", "params": [{"from": "******************************************", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "value": "0x0"}, ["stateDiff"], "latest"], "id": 22}, "response": {"jsonrpc": "2.0", "result": {"output": "0x", "stateDiff": {"******************************************": {"balance": {"*": {"from": "0x393f0f18385c0b29e", "to": "0x393f0f183860d5617"}}, "code": "=", "nonce": "=", "storage": {}}, "******************************************": {"balance": {"*": {"from": "0xffffffffffffffffffffffffffffffffd27fb671b", "to": "0xffffffffffffffffffffffffffffffffd27aec3a2"}}, "code": "=", "nonce": {"*": {"from": "0x1e", "to": "0x1f"}}, "storage": {}}}, "trace": [], "vmTrace": null}, "id": 22}, "statusCode": 200}