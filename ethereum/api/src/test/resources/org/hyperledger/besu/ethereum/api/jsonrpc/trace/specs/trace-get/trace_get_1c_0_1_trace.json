{"comment": "Self destruct called in constructor with balance being returned to sender (from field)", "request": {"jsonrpc": "2.0", "method": "trace_get", "params": ["0xbe1b55619f540ea8a7d7e9330ea642a195afe0d6d60ced6f463da67b46d3f503", ["0x0"]], "id": 371}, "response": {"jsonrpc": "2.0", "result": {"action": {"address": "******************************************", "balance": "0x0", "refundAddress": "******************************************"}, "blockHash": "0x96a82625443a337a5bfb912fd96977156f380c68b0d33040afffdb09c4cdbd52", "blockNumber": 28, "result": null, "subtraces": 0, "traceAddress": [0], "transactionHash": "0xbe1b55619f540ea8a7d7e9330ea642a195afe0d6d60ced6f463da67b46d3f503", "transactionPosition": 0, "type": "suicide"}, "id": 371}, "statusCode": 200}