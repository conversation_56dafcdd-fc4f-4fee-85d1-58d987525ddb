{"comment": "Simple value transfer", "request": {"jsonrpc": "2.0", "method": "trace_rawTransaction", "params": ["0xf8611781ef83fffff294000000000000000000000000000000000000099901801ca04ecb4fc65c930379a3688b9e34fb450141c141a6f8a651235ec4ddaaa62b0bd4a04d44121854a8747da882d3b66793bc3e8b0129cf6af18d43c4815ba2e3b097ef", ["trace"]], "id": 53}, "response": {"jsonrpc": "2.0", "result": {"output": "0x", "stateDiff": null, "trace": [{"action": {"callType": "call", "from": "******************************************", "gas": "0xffadea", "input": "0x", "to": "******************************************", "value": "0x1"}, "result": {"gasUsed": "0x0", "output": "0x"}, "subtraces": 0, "traceAddress": [], "type": "call"}], "vmTrace": null}, "id": 53}, "statusCode": 200}