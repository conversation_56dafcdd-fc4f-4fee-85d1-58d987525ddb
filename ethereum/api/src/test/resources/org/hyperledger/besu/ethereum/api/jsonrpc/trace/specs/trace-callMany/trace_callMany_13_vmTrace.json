{"comment": "'Deploy contract that will self-destruct when called.', 'call the prior contract then call log contract 00a0..00.', 'Deploy contract that will self-destruct when called.', 'call the prior contract then callcode log contract 00a0..00.', 'Deploy contract that will self-destruct when called.', 'call the prior contract then delegatecall log contract 00a0..00.'", "request": {"method": "trace_callMany", "params": [[[{"from": "******************************************", "value": "0x0", "gas": "0xfffff2", "gasPrice": "0xef", "data": "0x6004600C60003960046000F3600035FF"}, ["vmTrace"]], [{"from": "******************************************", "value": "0x0", "gas": "0xfffff2", "gasPrice": "0xef", "data": "0x60006000600060006000738f0483125fcb9aaaefa9209d8e9d7b9c8b9fb90f5AF1600060006000600060007300A00000000000000000000000000000000000005AF1"}, ["vmTrace"]], [{"from": "******************************************", "value": "0x0", "gas": "0xfffff2", "gasPrice": "0xef", "data": "0x6004600C60003960046000F3600035FF"}, ["vmTrace"]], [{"from": "******************************************", "value": "0x0", "gas": "0xfffff2", "gasPrice": "0xef", "data": "0x60006000600060006000732c2b9c9a4a25e24b174f26114e8926a9f2128fe45AF2600060006000600060007300A00000000000000000000000000000000000005AF2"}, ["vmTrace"]], [{"from": "******************************************", "value": "0x0", "gas": "0xfffff2", "gasPrice": "0xef", "data": "0x6004600C60003960046000F3600035FF"}, ["vmTrace"]], [{"from": "******************************************", "value": "0x0", "gas": "0xfffff2", "gasPrice": "0xef", "data": "0x600060006000600073fb88de099e13c3ed21f80a7a1e49f8caecf10df65AF460006000600060007300A00000000000000000000000000000000000005AF4"}, ["vmTrace"]]], "latest"], "id": 1, "jsonrpc": "2.0"}, "response": {"jsonrpc": "2.0", "result": [{"output": "0x600035ff", "stateDiff": null, "trace": [], "vmTrace": {"code": "0x6004600c60003960046000f3600035ff", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16723979}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0xc"], "store": null, "used": 16723976}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723973}, "pc": 4, "sub": null}, {"cost": 9, "ex": {"mem": {"data": "0x600035ff", "off": 0}, "push": [], "store": null, "used": 16723964}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16723961}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723958}, "pc": 9, "sub": null}, {"cost": 0, "ex": {"mem": null, "push": [], "store": null, "used": 16723958}, "pc": 11, "sub": null}]}}, {"output": "0x", "stateDiff": null, "trace": [], "vmTrace": {"code": "0x60006000600060006000738f0483125fcb9aaaefa9209d8e9d7b9c8b9fb90f5af1600060006000600060007300a00000000000000000000000000000000000005af1", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723491}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723488}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723485}, "pc": 4, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723482}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723479}, "pc": 8, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x8f0483125fcb9aaaefa9209d8e9d7b9c8b9fb90f"], "store": null, "used": 16723476}, "pc": 10, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xff2e12"], "store": null, "used": 16723474}, "pc": 31, "sub": null}, {"cost": 16462181, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16722774}, "pc": 32, "sub": {"code": "0x", "ops": []}}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16722771}, "pc": 33, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16722768}, "pc": 35, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16722765}, "pc": 37, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16722762}, "pc": 39, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16722759}, "pc": 41, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0xa0000000000000000000000000000000000000"], "store": null, "used": 16722756}, "pc": 43, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xff2b42"], "store": null, "used": 16722754}, "pc": 64, "sub": null}, {"cost": 16461472, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16721275}, "pc": 65, "sub": {"code": "0x608b60045360ff60016004a1", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x8b"], "store": null, "used": 16460769}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16460766}, "pc": 2, "sub": null}, {"cost": 6, "ex": {"mem": {"data": "0x8b", "off": 4}, "push": [], "store": null, "used": 16460760}, "pc": 4, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0xff"], "store": null, "used": 16460757}, "pc": 5, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16460754}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16460751}, "pc": 9, "sub": null}, {"cost": 758, "ex": {"mem": null, "push": [], "store": null, "used": 16459993}, "pc": 11, "sub": null}]}}]}}, {"output": "0x600035ff", "stateDiff": null, "trace": [], "vmTrace": {"code": "0x6004600c60003960046000f3600035ff", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16723979}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0xc"], "store": null, "used": 16723976}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723973}, "pc": 4, "sub": null}, {"cost": 9, "ex": {"mem": {"data": "0x600035ff", "off": 0}, "push": [], "store": null, "used": 16723964}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16723961}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723958}, "pc": 9, "sub": null}, {"cost": 0, "ex": {"mem": null, "push": [], "store": null, "used": 16723958}, "pc": 11, "sub": null}]}}, {"output": "0x", "stateDiff": null, "trace": [], "vmTrace": {"code": "0x60006000600060006000732c2b9c9a4a25e24b174f26114e8926a9f2128fe45af2600060006000600060007300a00000000000000000000000000000000000005af2", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723491}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723488}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723485}, "pc": 4, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723482}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723479}, "pc": 8, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x2c2b9c9a4a25e24b174f26114e8926a9f2128fe4"], "store": null, "used": 16723476}, "pc": 10, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xff2e12"], "store": null, "used": 16723474}, "pc": 31, "sub": null}, {"cost": 16462181, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16717768}, "pc": 32, "sub": {"code": "0x600035ff", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16461478}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16461475}, "pc": 2, "sub": null}, {"cost": 5000, "ex": {"mem": null, "push": [], "store": null, "used": 16456475}, "pc": 3, "sub": null}]}}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16717765}, "pc": 33, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16717762}, "pc": 35, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16717759}, "pc": 37, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16717756}, "pc": 39, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16717753}, "pc": 41, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0xa0000000000000000000000000000000000000"], "store": null, "used": 16717750}, "pc": 43, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xff17b4"], "store": null, "used": 16717748}, "pc": 64, "sub": null}, {"cost": 16456545, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16716269}, "pc": 65, "sub": {"code": "0x608b60045360ff60016004a1", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x8b"], "store": null, "used": 16455842}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16455839}, "pc": 2, "sub": null}, {"cost": 6, "ex": {"mem": {"data": "0x8b", "off": 4}, "push": [], "store": null, "used": 16455833}, "pc": 4, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0xff"], "store": null, "used": 16455830}, "pc": 5, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16455827}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16455824}, "pc": 9, "sub": null}, {"cost": 758, "ex": {"mem": null, "push": [], "store": null, "used": 16455066}, "pc": 11, "sub": null}]}}]}}, {"output": "0x600035ff", "stateDiff": null, "trace": [], "vmTrace": {"code": "0x6004600c60003960046000f3600035ff", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16723979}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0xc"], "store": null, "used": 16723976}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723973}, "pc": 4, "sub": null}, {"cost": 9, "ex": {"mem": {"data": "0x600035ff", "off": 0}, "push": [], "store": null, "used": 16723964}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16723961}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723958}, "pc": 9, "sub": null}, {"cost": 0, "ex": {"mem": null, "push": [], "store": null, "used": 16723958}, "pc": 11, "sub": null}]}}, {"output": "0x", "stateDiff": null, "trace": [], "vmTrace": {"code": "0x600060006000600073fb88de099e13c3ed21f80a7a1e49f8caecf10df65af460006000600060007300a00000000000000000000000000000000000005af4", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723531}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723528}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723525}, "pc": 4, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723522}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0xfb88de099e13c3ed21f80a7a1e49f8caecf10df6"], "store": null, "used": 16723519}, "pc": 8, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xff2e3d"], "store": null, "used": 16723517}, "pc": 29, "sub": null}, {"cost": 16462223, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16717811}, "pc": 30, "sub": {"code": "0x600035ff", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16461520}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16461517}, "pc": 2, "sub": null}, {"cost": 5000, "ex": {"mem": null, "push": [], "store": null, "used": 16456517}, "pc": 3, "sub": null}]}}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16717808}, "pc": 31, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16717805}, "pc": 33, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16717802}, "pc": 35, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16717799}, "pc": 37, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0xa0000000000000000000000000000000000000"], "store": null, "used": 16717796}, "pc": 39, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xff17e2"], "store": null, "used": 16717794}, "pc": 60, "sub": null}, {"cost": 16456590, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16716315}, "pc": 61, "sub": {"code": "0x608b60045360ff60016004a1", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x8b"], "store": null, "used": 16455887}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16455884}, "pc": 2, "sub": null}, {"cost": 6, "ex": {"mem": {"data": "0x8b", "off": 4}, "push": [], "store": null, "used": 16455878}, "pc": 4, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0xff"], "store": null, "used": 16455875}, "pc": 5, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16455872}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16455869}, "pc": 9, "sub": null}, {"cost": 758, "ex": {"mem": null, "push": [], "store": null, "used": 16455111}, "pc": 11, "sub": null}]}}]}}], "id": 1}, "statusCode": 200}