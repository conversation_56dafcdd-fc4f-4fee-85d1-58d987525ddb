{"request": {"jsonrpc": "2.0", "method": "trace_replayBlockTransactions", "params": ["0x4", ["vmTrace"]], "id": 415}, "response": {"jsonrpc": "2.0", "result": [{"output": "0x", "stateDiff": null, "trace": [], "transactionHash": "0x4de634fe767d1f6d0512ca0c9c0a054d3a2596f7cdd7c1eea5f93046a740b3c7", "vmTrace": {"code": "0x6020356000355560603560403555", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755639}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16755636}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755633}, "pc": 3, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16755630}, "pc": 5, "sub": null}, {"cost": 20000, "ex": {"mem": null, "push": [], "store": {"key": "0x1", "val": "0x1"}, "used": 16735630}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x60"], "store": null, "used": 16735627}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x2"], "store": null, "used": 16735624}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x40"], "store": null, "used": 16735621}, "pc": 10, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x2"], "store": null, "used": 16735618}, "pc": 12, "sub": null}, {"cost": 20000, "ex": {"mem": null, "push": [], "store": {"key": "0x2", "val": "0x2"}, "used": 16715618}, "pc": 13, "sub": null}]}}, {"output": "0x", "stateDiff": null, "trace": [], "transactionHash": "0xf882ec206292910527fd7095e59a1ca027b873296f1eba3886aa1addc4ff0ab9", "vmTrace": {"code": "0x6020356000355560603560403555", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755639}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x3"], "store": null, "used": 16755636}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755633}, "pc": 3, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16755630}, "pc": 5, "sub": null}, {"cost": 5000, "ex": {"mem": null, "push": [], "store": {"key": "0x1", "val": "0x3"}, "used": 16750630}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x60"], "store": null, "used": 16750627}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16750624}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x40"], "store": null, "used": 16750621}, "pc": 10, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x2"], "store": null, "used": 16750618}, "pc": 12, "sub": null}, {"cost": 5000, "ex": {"mem": null, "push": [], "store": {"key": "0x2", "val": "0x4"}, "used": 16745618}, "pc": 13, "sub": null}]}}, {"output": "0x", "stateDiff": null, "trace": [], "transactionHash": "0x7ca6bf869e8882216f7443accb8d642df41af5bfa3a0e63bf03be2cfe629a030", "vmTrace": {"code": "0x6020356000355560603560403555", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755651}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x3"], "store": null, "used": 16755648}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755645}, "pc": 3, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16755642}, "pc": 5, "sub": null}, {"cost": 800, "ex": {"mem": null, "push": [], "store": {"key": "0x1", "val": "0x3"}, "used": 16754842}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x60"], "store": null, "used": 16754839}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16754836}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x40"], "store": null, "used": 16754833}, "pc": 10, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16754830}, "pc": 12, "sub": null}, {"cost": 5000, "ex": {"mem": null, "push": [], "store": {"key": "0x1", "val": "0x0"}, "used": 16749830}, "pc": 13, "sub": null}]}}], "id": 415}, "statusCode": 200}