{"comment": "'Set contract storage (key,value)'s: (1,1),(2,2)', 'Set contract storage (key,value)'s: (1,3),(2,4)', 'Set contract storage (key,value)'s: (1,3),(1,0)', 'Clear contract storage keys 1 and 2'", "request": {"method": "trace_callMany", "params": [[[{"from": "******************************************", "value": "0x0", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "data": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002"}, ["trace"]], [{"from": "******************************************", "value": "0x0", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "data": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000004"}, ["trace"]], [{"from": "******************************************", "value": "0x0", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "data": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000"}, ["trace"]], [{"from": "******************************************", "value": "0x0", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "data": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000"}, ["trace"]]], "latest"], "id": 1, "jsonrpc": "2.0"}, "response": {"jsonrpc": "2.0", "result": [{"output": "0x0000000000000000000000000000000000000000000000000000000000000001", "stateDiff": null, "trace": [{"action": {"callType": "call", "from": "******************************************", "gas": "0xffab2e", "input": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002", "to": "******************************************", "value": "0x0"}, "result": {"gasUsed": "0x9f57", "output": "0x0000000000000000000000000000000000000000000000000000000000000001"}, "subtraces": 1, "traceAddress": [], "type": "call"}, {"action": {"callType": "delegatecall", "from": "******************************************", "gas": "0xfba995", "input": "0x0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002", "to": "0x0010000000000000000000000000000000000000", "value": "0x0"}, "result": {"gasUsed": "0x9c58", "output": "0x"}, "subtraces": 0, "traceAddress": [0], "type": "call"}], "vmTrace": null}, {"output": "0x0000000000000000000000000000000000000000000000000000000000000001", "stateDiff": null, "trace": [{"action": {"callType": "call", "from": "******************************************", "gas": "0xffab2e", "input": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000004", "to": "******************************************", "value": "0x0"}, "result": {"gasUsed": "0x2a27", "output": "0x0000000000000000000000000000000000000000000000000000000000000001"}, "subtraces": 1, "traceAddress": [], "type": "call"}, {"action": {"callType": "delegatecall", "from": "******************************************", "gas": "0xfba995", "input": "0x0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000004", "to": "0x0010000000000000000000000000000000000000", "value": "0x0"}, "result": {"gasUsed": "0x2728", "output": "0x"}, "subtraces": 0, "traceAddress": [0], "type": "call"}], "vmTrace": null}, {"output": "0x0000000000000000000000000000000000000000000000000000000000000001", "stateDiff": null, "trace": [{"action": {"callType": "call", "from": "******************************************", "gas": "0xffab3a", "input": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000", "to": "******************************************", "value": "0x0"}, "result": {"gasUsed": "0x19bf", "output": "0x0000000000000000000000000000000000000000000000000000000000000001"}, "subtraces": 1, "traceAddress": [], "type": "call"}, {"action": {"callType": "delegatecall", "from": "******************************************", "gas": "0xfba9a0", "input": "0x0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000", "to": "0x0010000000000000000000000000000000000000", "value": "0x0"}, "result": {"gasUsed": "0x16c0", "output": "0x"}, "subtraces": 0, "traceAddress": [0], "type": "call"}], "vmTrace": null}, {"output": "0x0000000000000000000000000000000000000000000000000000000000000001", "stateDiff": null, "trace": [{"action": {"callType": "call", "from": "******************************************", "gas": "0xffab46", "input": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000", "to": "******************************************", "value": "0x0"}, "result": {"gasUsed": "0x19bf", "output": "0x0000000000000000000000000000000000000000000000000000000000000001"}, "subtraces": 1, "traceAddress": [], "type": "call"}, {"action": {"callType": "delegatecall", "from": "******************************************", "gas": "0xfba9ac", "input": "0x0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000", "to": "0x0010000000000000000000000000000000000000", "value": "0x0"}, "result": {"gasUsed": "0x16c0", "output": "0x"}, "subtraces": 0, "traceAddress": [0], "type": "call"}], "vmTrace": null}], "id": 1}, "statusCode": 200}