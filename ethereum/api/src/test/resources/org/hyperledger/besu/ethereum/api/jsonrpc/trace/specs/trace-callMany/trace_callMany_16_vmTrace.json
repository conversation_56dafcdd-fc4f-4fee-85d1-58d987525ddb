{"comment": "'Set contract storage (key,value)'s: (1,1),(2,2)', 'Set contract storage (key,value)'s: (1,3),(2,4)', 'Set contract storage (key,value)'s: (1,3),(1,0)', 'Clear contract storage keys 1 and 2'", "request": {"method": "trace_callMany", "params": [[[{"from": "******************************************", "value": "0x0", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "data": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002"}, ["vmTrace"]], [{"from": "******************************************", "value": "0x0", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "data": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000004"}, ["vmTrace"]], [{"from": "******************************************", "value": "0x0", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "data": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000"}, ["vmTrace"]], [{"from": "******************************************", "value": "0x0", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "data": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000"}, ["vmTrace"]]], "latest"], "id": 1, "jsonrpc": "2.0"}, "response": {"jsonrpc": "2.0", "result": [{"output": "0x0000000000000000000000000000000000000000000000000000000000000001", "stateDiff": null, "trace": [], "vmTrace": {"code": "0x602060006020360380602060003760006000355af460206000f3", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755499}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755496}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755493}, "pc": 4, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xa0"], "store": null, "used": 16755491}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x80"], "store": null, "used": 16755488}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x80", "0x80"], "store": null, "used": 16755485}, "pc": 8, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755482}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755479}, "pc": 11, "sub": null}, {"cost": 27, "ex": {"mem": {"data": "0x0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002", "off": 0}, "push": [], "store": null, "used": 16755452}, "pc": 13, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755449}, "pc": 14, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755446}, "pc": 16, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x10000000000000000000000000000000000000"], "store": null, "used": 16755443}, "pc": 18, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xffaaf1"], "store": null, "used": 16755441}, "pc": 19, "sub": null}, {"cost": 16493649, "ex": {"mem": {"data": "0x0000000000000000000000000000000000000000000000000000000000000001", "off": 0}, "push": ["0x1"], "store": null, "used": 16714717}, "pc": 20, "sub": {"code": "0x6020356000355560603560403555", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16492946}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16492943}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16492940}, "pc": 3, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16492937}, "pc": 5, "sub": null}, {"cost": 20000, "ex": {"mem": null, "push": [], "store": {"key": "0x1", "val": "0x1"}, "used": 16472937}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x60"], "store": null, "used": 16472934}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x2"], "store": null, "used": 16472931}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x40"], "store": null, "used": 16472928}, "pc": 10, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x2"], "store": null, "used": 16472925}, "pc": 12, "sub": null}, {"cost": 20000, "ex": {"mem": null, "push": [], "store": {"key": "0x2", "val": "0x2"}, "used": 16452925}, "pc": 13, "sub": null}]}}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16714714}, "pc": 21, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16714711}, "pc": 23, "sub": null}, {"cost": 0, "ex": {"mem": null, "push": [], "store": null, "used": 16714711}, "pc": 25, "sub": null}]}}, {"output": "0x0000000000000000000000000000000000000000000000000000000000000001", "stateDiff": null, "trace": [], "vmTrace": {"code": "0x602060006020360380602060003760006000355af460206000f3", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755499}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755496}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755493}, "pc": 4, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xa0"], "store": null, "used": 16755491}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x80"], "store": null, "used": 16755488}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x80", "0x80"], "store": null, "used": 16755485}, "pc": 8, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755482}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755479}, "pc": 11, "sub": null}, {"cost": 27, "ex": {"mem": {"data": "0x0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000004", "off": 0}, "push": [], "store": null, "used": 16755452}, "pc": 13, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755449}, "pc": 14, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755446}, "pc": 16, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x10000000000000000000000000000000000000"], "store": null, "used": 16755443}, "pc": 18, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xffaaf1"], "store": null, "used": 16755441}, "pc": 19, "sub": null}, {"cost": 16493649, "ex": {"mem": {"data": "0x0000000000000000000000000000000000000000000000000000000000000001", "off": 0}, "push": ["0x1"], "store": null, "used": 16744717}, "pc": 20, "sub": {"code": "0x6020356000355560603560403555", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16492946}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x3"], "store": null, "used": 16492943}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16492940}, "pc": 3, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16492937}, "pc": 5, "sub": null}, {"cost": 5000, "ex": {"mem": null, "push": [], "store": {"key": "0x1", "val": "0x3"}, "used": 16487937}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x60"], "store": null, "used": 16487934}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16487931}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x40"], "store": null, "used": 16487928}, "pc": 10, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x2"], "store": null, "used": 16487925}, "pc": 12, "sub": null}, {"cost": 5000, "ex": {"mem": null, "push": [], "store": {"key": "0x2", "val": "0x4"}, "used": 16482925}, "pc": 13, "sub": null}]}}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16744714}, "pc": 21, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16744711}, "pc": 23, "sub": null}, {"cost": 0, "ex": {"mem": null, "push": [], "store": null, "used": 16744711}, "pc": 25, "sub": null}]}}, {"output": "0x0000000000000000000000000000000000000000000000000000000000000001", "stateDiff": null, "trace": [], "vmTrace": {"code": "0x602060006020360380602060003760006000355af460206000f3", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755511}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755508}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755505}, "pc": 4, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xa0"], "store": null, "used": 16755503}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x80"], "store": null, "used": 16755500}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x80", "0x80"], "store": null, "used": 16755497}, "pc": 8, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755494}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755491}, "pc": 11, "sub": null}, {"cost": 27, "ex": {"mem": {"data": "0x0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000", "off": 0}, "push": [], "store": null, "used": 16755464}, "pc": 13, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755461}, "pc": 14, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755458}, "pc": 16, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x10000000000000000000000000000000000000"], "store": null, "used": 16755455}, "pc": 18, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xffaafd"], "store": null, "used": 16755453}, "pc": 19, "sub": null}, {"cost": 16493660, "ex": {"mem": {"data": "0x0000000000000000000000000000000000000000000000000000000000000001", "off": 0}, "push": ["0x1"], "store": null, "used": 16748929}, "pc": 20, "sub": {"code": "0x6020356000355560603560403555", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16492957}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x3"], "store": null, "used": 16492954}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16492951}, "pc": 3, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16492948}, "pc": 5, "sub": null}, {"cost": 800, "ex": {"mem": null, "push": [], "store": {"key": "0x1", "val": "0x3"}, "used": 16492148}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x60"], "store": null, "used": 16492145}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16492142}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x40"], "store": null, "used": 16492139}, "pc": 10, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16492136}, "pc": 12, "sub": null}, {"cost": 5000, "ex": {"mem": null, "push": [], "store": {"key": "0x1", "val": "0x0"}, "used": 16487136}, "pc": 13, "sub": null}]}}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16748926}, "pc": 21, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16748923}, "pc": 23, "sub": null}, {"cost": 0, "ex": {"mem": null, "push": [], "store": null, "used": 16748923}, "pc": 25, "sub": null}]}}, {"output": "0x0000000000000000000000000000000000000000000000000000000000000001", "stateDiff": null, "trace": [], "vmTrace": {"code": "0x602060006020360380602060003760006000355af460206000f3", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755523}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755520}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755517}, "pc": 4, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xa0"], "store": null, "used": 16755515}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x80"], "store": null, "used": 16755512}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x80", "0x80"], "store": null, "used": 16755509}, "pc": 8, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16755506}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755503}, "pc": 11, "sub": null}, {"cost": 27, "ex": {"mem": {"data": "0x0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000", "off": 0}, "push": [], "store": null, "used": 16755476}, "pc": 13, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755473}, "pc": 14, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755470}, "pc": 16, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x10000000000000000000000000000000000000"], "store": null, "used": 16755467}, "pc": 18, "sub": null}, {"cost": 2, "ex": {"mem": null, "push": ["0xffab09"], "store": null, "used": 16755465}, "pc": 19, "sub": null}, {"cost": 16493672, "ex": {"mem": {"data": "0x0000000000000000000000000000000000000000000000000000000000000001", "off": 0}, "push": ["0x1"], "store": null, "used": 16748941}, "pc": 20, "sub": {"code": "0x6020356000355560603560403555", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16492969}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16492966}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16492963}, "pc": 3, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16492960}, "pc": 5, "sub": null}, {"cost": 800, "ex": {"mem": null, "push": [], "store": {"key": "0x1", "val": "0x0"}, "used": 16492160}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x60"], "store": null, "used": 16492157}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16492154}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x40"], "store": null, "used": 16492151}, "pc": 10, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x2"], "store": null, "used": 16492148}, "pc": 12, "sub": null}, {"cost": 5000, "ex": {"mem": null, "push": [], "store": {"key": "0x2", "val": "0x0"}, "used": 16487148}, "pc": 13, "sub": null}]}}, {"cost": 3, "ex": {"mem": null, "push": ["0x20"], "store": null, "used": 16748938}, "pc": 21, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16748935}, "pc": 23, "sub": null}, {"cost": 0, "ex": {"mem": null, "push": [], "store": null, "used": 16748935}, "pc": 25, "sub": null}]}}], "id": 1}, "statusCode": 200}