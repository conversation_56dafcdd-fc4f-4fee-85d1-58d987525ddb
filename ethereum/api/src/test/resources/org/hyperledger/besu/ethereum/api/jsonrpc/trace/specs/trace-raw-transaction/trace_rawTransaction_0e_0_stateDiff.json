{"comment": "Increments contract storage 0 by 1", "request": {"jsonrpc": "2.0", "method": "trace_rawTransaction", "params": ["0xf8611e81ef83fffff294009000000000000000000000000000000000000080801ba06d4cf2dbd66884ca1256f3ca5e78a43ab9a0a641c4d0a920237d2533e791eccfa05b100df1d529a311d3222e2b05be800f60d4b0dc869236731406d40d17348489", ["stateDiff"]], "id": 14}, "response": {"jsonrpc": "2.0", "result": {"output": "0x04", "stateDiff": {"******************************************": {"balance": {"*": {"from": "0x393f0f18385c0b29e", "to": "0x393f0f18386228af0"}}, "code": "=", "nonce": "=", "storage": {}}, "******************************************": {"balance": "=", "code": "=", "nonce": "=", "storage": {"******************************************000000000000000000000000": {"*": {"from": "******************************************000000000000000000000003", "to": "******************************************000000000000000000000004"}}}}, "******************************************": {"balance": {"*": {"from": "0xffffffffffffffffffffffffffffffffd27fb671b", "to": "0xffffffffffffffffffffffffffffffffd27998ec9"}}, "code": "=", "nonce": {"*": {"from": "0x1e", "to": "0x1f"}}, "storage": {}}}, "trace": [], "vmTrace": null}, "id": 14}, "statusCode": 200}