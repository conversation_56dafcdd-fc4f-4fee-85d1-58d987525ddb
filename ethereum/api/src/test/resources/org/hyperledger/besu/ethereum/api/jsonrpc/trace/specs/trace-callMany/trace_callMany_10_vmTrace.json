{"comment": "'Various exceptional halt cases. Invalid OPCODE (0x1F).', 'Various exceptional halt cases. INSUFFICIENT_STACK_ITEMS.', 'Various exceptional halt cases. INVALID_JUMP_DESTINATION.'", "request": {"method": "trace_callMany", "params": [[[{"from": "******************************************", "value": "0x0", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef"}, ["vmTrace"]], [{"from": "******************************************", "value": "0x0", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef"}, ["vmTrace"]], [{"from": "******************************************", "value": "0x0", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef"}, ["vmTrace"]]], "latest"], "id": 1, "jsonrpc": "2.0"}, "response": {"jsonrpc": "2.0", "result": [{"output": "0x", "stateDiff": null, "trace": [], "vmTrace": {"code": "0x60011f", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16756199}, "pc": 0, "sub": null}]}}, {"output": "0x", "stateDiff": null, "trace": [], "vmTrace": {"code": "0x600120", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16756199}, "pc": 0, "sub": null}]}}, {"output": "0x", "stateDiff": null, "trace": [], "vmTrace": {"code": "0x60ff5660016002", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0xff"], "store": null, "used": 16756199}, "pc": 0, "sub": null}, {"cost": 8, "ex": {"mem": null, "push": [], "store": null, "used": 16756191}, "pc": 2, "sub": null}]}}], "id": 1}, "statusCode": 200}