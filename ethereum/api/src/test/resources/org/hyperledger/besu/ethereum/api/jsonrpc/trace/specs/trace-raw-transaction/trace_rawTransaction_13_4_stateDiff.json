{"comment": "Deploy contract that will self-destruct when called.", "request": {"jsonrpc": "2.0", "method": "trace_rawTransaction", "params": ["0xf85d1781ef83fffff28080906004600c60003960046000f3600035ff1ca0de34cea135096251fed1e795829a7fcc86c83e1d0c4deeff6076f58ecb08374ba0683c9a7f8cf19bb073cf12dd99a72b946ba4b2adb9ece7d8ad357860a4d6e973", ["stateDiff"]], "id": 29}, "response": {"jsonrpc": "2.0", "result": {"output": "0x600035ff", "stateDiff": {"******************************************": {"balance": {"*": {"from": "0x393f0f18385c0b29e", "to": "0x393f0f1838685c9c2"}}, "code": "=", "nonce": "=", "storage": {}}, "******************************************": {"balance": {"+": "0x0"}, "code": {"+": "0x600035ff"}, "nonce": {"+": "0x1"}, "storage": {}}, "******************************************": {"balance": {"*": {"from": "0xefffffffffffe28d07a0be63b", "to": "0xefffffffffffe28d07946cf17"}}, "code": "=", "nonce": {"*": {"from": "0x17", "to": "0x18"}}, "storage": {}}}, "trace": [], "vmTrace": null}, "id": 29}, "statusCode": 200}