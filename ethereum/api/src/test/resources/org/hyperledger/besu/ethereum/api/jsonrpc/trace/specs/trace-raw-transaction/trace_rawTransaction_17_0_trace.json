{"comment": "Proxy call to another contract. Using STATICCALL.", "request": {"jsonrpc": "2.0", "method": "trace_rawTransaction", "params": ["0xf8f5170183030d4094012000000000000000000000000000000000000080b8940000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001200000000000000000000000000000000000000000000000000000000000000120785e45de3d6be050ba3c4d33ff0bb2d010ace3b1dfe9c49f4c7a8075102fa19a86c010ace3b1dfe9c49f4c7a8075102fa19a86d1ca0433a07676e5a08df53d1196ccb18b7085bd693b20eb4de343183b6f0f9c5f7aaa0458752f753ede06ad2147e61154192132cde051019d343cd48aefc2cfd69cb1d", ["trace"]], "id": 94}, "response": {"jsonrpc": "2.0", "result": {"output": "0x0000000000000000000000000000000000000000000000000000000000000012", "stateDiff": null, "trace": [{"action": {"callType": "call", "from": "******************************************", "gas": "0x2b654", "input": "0x0000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001200000000000000000000000000000000000000000000000000000000000000120785e45de3d6be050ba3c4d33ff0bb2d010ace3b1dfe9c49f4c7a8075102fa19a86c010ace3b1dfe9c49f4c7a8075102fa19a86d", "to": "0x0120000000000000000000000000000000000000", "value": "0x0"}, "result": {"gasUsed": "0x301", "output": "0x0000000000000000000000000000000000000000000000000000000000000012"}, "subtraces": 1, "traceAddress": [], "type": "call"}, {"action": {"callType": "staticcall", "from": "0x0120000000000000000000000000000000000000", "gas": "0x2a88c", "input": "0x", "to": "0x0000000000000000000000000000000000000010", "value": "0x0"}, "result": {"gasUsed": "0x0", "output": "0x"}, "subtraces": 0, "traceAddress": [0], "type": "call"}], "vmTrace": null}, "id": 94}, "statusCode": 200}