{"comment": "call the prior contract then callcode log contract 00a0..00.", "request": {"jsonrpc": "2.0", "method": "trace_get", "params": ["0x4c253746668dca6ac3f7b9bc18248b558a95b5fc881d140872c2dff984d344a7", ["0x0"]], "id": 292}, "response": {"jsonrpc": "2.0", "result": {"action": {"callType": "callcode", "from": "******************************************", "gas": "0xfb2ea9", "input": "0x", "to": "******************************************", "value": "0x0"}, "blockHash": "0x425ea2cf9bb89c09f131ab531958c4d189b5d047fd924d74f25d49cf47a5a8f1", "blockNumber": 19, "result": {"gasUsed": "0x138e", "output": "0x"}, "subtraces": 1, "traceAddress": [0], "transactionHash": "0x4c253746668dca6ac3f7b9bc18248b558a95b5fc881d140872c2dff984d344a7", "transactionPosition": 3, "type": "call"}, "id": 292}, "statusCode": 200}