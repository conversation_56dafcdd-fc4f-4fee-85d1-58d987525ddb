{"comment": "CREATE", "request": {"jsonrpc": "2.0", "method": "trace_call", "params": [{"from": "******************************************", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "value": "0x0"}, ["trace"], "latest"], "id": 31}, "response": {"jsonrpc": "2.0", "result": {"output": "0x", "stateDiff": null, "trace": [{"action": {"callType": "call", "from": "******************************************", "gas": "0xffadea", "input": "0x", "to": "******************************************", "value": "0x0"}, "result": {"gasUsed": "0x131ae", "output": "0x"}, "subtraces": 1, "traceAddress": [], "type": "call"}, {"action": {"from": "******************************************", "gas": "0xfb3412", "init": "0x600160015560015460025561ffff6000526002601ef3", "value": "0x0"}, "result": {"address": "******************************************", "code": "0xffff", "gasUsed": "0xa10e"}, "subtraces": 0, "traceAddress": [0], "type": "create"}], "vmTrace": null}, "id": 31}, "statusCode": 200}