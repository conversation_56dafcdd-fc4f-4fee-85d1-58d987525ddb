{"comment": "'Call Precompiled contract directly'", "request": {"method": "trace_callMany", "params": [[[{"from": "******************************************", "value": "0x0", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef"}, ["stateDiff"]]], "latest"], "id": 1, "jsonrpc": "2.0"}, "response": {"jsonrpc": "2.0", "result": [{"output": "0x", "stateDiff": {"******************************************": {"balance": {"*": {"from": "0x393f0f18385c0b29e", "to": "0x393f0f183861838de"}}, "code": "=", "nonce": "=", "storage": {}}, "******************************************": {"balance": {"*": {"from": "0xefffffffffffe28d07a0be63b", "to": "0xefffffffffffe28d079b45ffb"}}, "code": "=", "nonce": {"*": {"from": "0x17", "to": "0x18"}}, "storage": {}}}, "trace": [], "vmTrace": null}], "id": 1}, "statusCode": 200}