{"comment": "Increments contract storage 0 by 1", "request": {"jsonrpc": "2.0", "method": "trace_call", "params": [{"from": "******************************************", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "value": "0x0"}, ["vmTrace"], "latest"], "id": 67}, "response": {"jsonrpc": "2.0", "result": {"output": "0x04", "stateDiff": null, "trace": [], "vmTrace": {"code": "0x6000546001018060005360005560016000f3", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16756199}, "pc": 0, "sub": null}, {"cost": 800, "ex": {"mem": null, "push": ["0x3"], "store": null, "used": 16755399}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16755396}, "pc": 3, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16755393}, "pc": 5, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x4", "0x4"], "store": null, "used": 16755390}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755387}, "pc": 7, "sub": null}, {"cost": 6, "ex": {"mem": {"data": "0x04", "off": 0}, "push": [], "store": null, "used": 16755381}, "pc": 9, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16755378}, "pc": 10, "sub": null}, {"cost": 5000, "ex": {"mem": null, "push": [], "store": {"key": "0x0", "val": "0x4"}, "used": 16750378}, "pc": 12, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x1"], "store": null, "used": 16750375}, "pc": 13, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16750372}, "pc": 15, "sub": null}, {"cost": 0, "ex": {"mem": null, "push": [], "store": null, "used": 16750372}, "pc": 17, "sub": null}]}}, "id": 67}, "statusCode": 200}