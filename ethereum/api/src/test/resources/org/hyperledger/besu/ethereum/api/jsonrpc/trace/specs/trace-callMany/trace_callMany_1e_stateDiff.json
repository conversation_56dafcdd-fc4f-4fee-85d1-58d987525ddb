{"comment": "'Transaction to test refund_CallToSuicideTwice_d0g0v0_Istanbul'", "request": {"method": "trace_callMany", "params": [[[{"from": "******************************************", "value": "0xa", "to": "******************************************", "gas": "0x989680", "gasPrice": "0x1", "data": "******************************************0000000000000000000001f4"}, ["stateDiff"]]], "latest"], "id": 1, "jsonrpc": "2.0"}, "response": {"jsonrpc": "2.0", "result": [{"output": "0x", "stateDiff": {"******************************************": {"balance": {"*": {"from": "0x393f0f18385c0b29e", "to": "0x393f0f18385c111f1"}}, "code": "=", "nonce": "=", "storage": {}}, "******************************************": {"balance": {"*": {"from": "0xde0b6b3a764000a", "to": "0xde0b6b3a7640014"}}, "code": "=", "nonce": "=", "storage": {}}, "******************************************": {"balance": {"*": {"from": "0xffffffffffffffffffffffffffffffffd27fb671b", "to": "0xffffffffffffffffffffffffffffffffd27fb07be"}}, "code": "=", "nonce": {"*": {"from": "0x1e", "to": "0x1f"}}, "storage": {}}}, "trace": [], "vmTrace": null}], "id": 1}, "statusCode": 200}