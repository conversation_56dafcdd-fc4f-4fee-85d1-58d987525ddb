{"comment": "Deploy contract that will self-destruct when called.", "request": {"jsonrpc": "2.0", "method": "trace_call", "params": [{"from": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "value": "0x0", "data": "0x6004600C60003960046000F3600035FF"}, ["vmTrace"], "latest"], "id": 80}, "response": {"jsonrpc": "2.0", "result": {"output": "0x600035ff", "stateDiff": null, "trace": [], "vmTrace": {"code": "0x6004600c60003960046000f3600035ff", "ops": [{"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16723979}, "pc": 0, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0xc"], "store": null, "used": 16723976}, "pc": 2, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723973}, "pc": 4, "sub": null}, {"cost": 9, "ex": {"mem": {"data": "0x600035ff", "off": 0}, "push": [], "store": null, "used": 16723964}, "pc": 6, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x4"], "store": null, "used": 16723961}, "pc": 7, "sub": null}, {"cost": 3, "ex": {"mem": null, "push": ["0x0"], "store": null, "used": 16723958}, "pc": 9, "sub": null}, {"cost": 0, "ex": {"mem": null, "push": [], "store": null, "used": 16723958}, "pc": 11, "sub": null}]}}, "id": 80}, "statusCode": 200}