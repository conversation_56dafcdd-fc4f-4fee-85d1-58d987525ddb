{"comment": "Proxy call to another contract - 1 level deep.", "request": {"jsonrpc": "2.0", "method": "trace_get", "params": ["0xa29f9d6a4f183f4c22c4857544a9a6b69c48d7bb8a97652be06e50bb69470666", []], "id": 222}, "response": {"jsonrpc": "2.0", "result": {"action": {"callType": "call", "from": "******************************************", "gas": "0xffacc6", "input": "0x0000000000000000000000000030000000000000000000000000000000000000f000000000000000000000000000000000000000000000000000000000000001", "to": "******************************************", "value": "0x0"}, "blockHash": "0x35a8bb01d5da479278581033c58e242e6dd65606d4b5ee7332e074c849af2fe8", "blockNumber": 8, "result": {"gasUsed": "0x30a", "output": "0xf000000000000000000000000000000000000000000000000000000000000002"}, "subtraces": 1, "traceAddress": [], "transactionHash": "0xa29f9d6a4f183f4c22c4857544a9a6b69c48d7bb8a97652be06e50bb69470666", "transactionPosition": 0, "type": "call"}, "id": 222}, "statusCode": 200}