{"comment": "Set contract storage (key,value)'s: (1,3),(2,4)", "request": {"jsonrpc": "2.0", "method": "trace_call", "params": [{"from": "******************************************", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "value": "0x0", "data": "******************************************000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000004"}, ["stateDiff"], "latest"], "id": 3}, "response": {"jsonrpc": "2.0", "result": {"output": "0x", "stateDiff": {"******************************************": {"balance": {"*": {"from": "0x393f0f18385c0b29e", "to": "0x393f0f18386a1490e"}}, "code": "=", "nonce": "=", "storage": {}}, "******************************************": {"balance": "=", "code": "=", "nonce": "=", "storage": {"******************************************000000000000000000000001": {"*": {"from": "******************************************000000000000000000000000", "to": "******************************************000000000000000000000003"}}, "******************************************000000000000000000000002": {"*": {"from": "******************************************000000000000000000000000", "to": "******************************************000000000000000000000004"}}}}, "******************************************": {"balance": {"*": {"from": "0xefffffffffffe28d07a0be63b", "to": "0xefffffffffffe28d0792b4fcb"}}, "code": "=", "nonce": {"*": {"from": "0x17", "to": "0x18"}}, "storage": {}}}, "trace": [], "vmTrace": null}, "id": 3}, "statusCode": 200}