{"comment": "'Proxy call to another contract - 1 level deep with gas refund.'", "request": {"method": "trace_callMany", "params": [[[{"from": "******************************************", "value": "0x0", "to": "******************************************", "gas": "0xfffff2", "gasPrice": "0xef", "data": "0x00000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000"}, ["stateDiff"]]], "latest"], "id": 1, "jsonrpc": "2.0"}, "response": {"jsonrpc": "2.0", "result": [{"output": "******************************************000000000000000000000001", "stateDiff": {"******************************************": {"balance": {"*": {"from": "0x393f0f18385c0b29e", "to": "0x393f0f1838618847d"}}, "code": "=", "nonce": "=", "storage": {}}, "******************************************": {"balance": {"*": {"from": "0xffffffffffffffffffffffffffffffffd27fb671b", "to": "0xffffffffffffffffffffffffffffffffd27a3953c"}}, "code": "=", "nonce": {"*": {"from": "0x1e", "to": "0x1f"}}, "storage": {}}}, "trace": [], "vmTrace": null}], "id": 1}, "statusCode": 200}