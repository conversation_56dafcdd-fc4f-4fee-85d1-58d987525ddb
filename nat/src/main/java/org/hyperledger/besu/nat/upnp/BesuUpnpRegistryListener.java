/*
 * Copyright ConsenSys AG.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
package org.hyperledger.besu.nat.upnp;

import org.jupnp.model.meta.LocalDevice;
import org.jupnp.model.meta.RemoteDevice;
import org.jupnp.registry.Registry;
import org.jupnp.registry.RegistryListener;

class BesuUpnpRegistryListener implements RegistryListener {

  @Override
  public void remoteDeviceDiscoveryStarted(final Registry registry, final RemoteDevice device) {}

  @Override
  public void remoteDeviceDiscoveryFailed(
      final Registry registry, final RemoteDevice device, final Exception ex) {}

  @Override
  public void remoteDeviceAdded(final Registry registry, final RemoteDevice device) {}

  @Override
  public void remoteDeviceUpdated(final Registry registry, final RemoteDevice device) {}

  @Override
  public void remoteDeviceRemoved(final Registry registry, final RemoteDevice device) {}

  @Override
  public void localDeviceAdded(final Registry registry, final LocalDevice device) {}

  @Override
  public void localDeviceRemoved(final Registry registry, final LocalDevice device) {}

  @Override
  public void beforeShutdown(final Registry registry) {}

  @Override
  public void afterShutdown() {}
}
